* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.container {
    max-width: 1140px;
    margin: 0 auto;
}

header {
    background: url('https://images.unsplash.com/photo-1533757879476-8f4a3cb1ae4b?q=80&w=1374&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D');
    background-size: cover;
    background-position: center;
    height: 500px;
    color: #fff;
}

nav {
    display: flex;
    justify-content: space-between;
    padding: 1rem 0;
}

nav a {
    color: #fff;
    text-decoration: none;
}

.logo a {
    font-size: 28px;
}

.header-info {
    margin-top: 5rem;
    width: 500px;
}

.header-info h3 {
    font-size: 3rem;
    font-weight: lighter;
}

.header-btn {
    margin-top: 1rem;
}

.header-btn input {
    padding: 0.5rem;
    width: 230px;
    border: none;
    border-radius: 5px;
}

.header-btn button {
    padding: 0.5rem;
    width: 150px;
    background-color: aqua;
    color: #fff;
    border: none;
    border-radius: 5px;
}

.about {
    text-align: center;
    padding: 3rem 0;
}

.about-con {
    padding: 0 10rem;
}

.about-title {
    color: aqua;
    font-size: 2rem;
}

.about-dest {
    color: #b4b4b4;
}

.about-img {
    margin: 2rem 0;
}

.pricing {
    background-color: aqua;
    color: #fff;
    text-align: center;
    padding: 3rem 0;
}

.pricing-info h3 {
    font-weight: lighter;
    font-size: 2rem;
}

.pricing-info p {
    margin: 1rem 0;
}

.pricing-table {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-top: 3rem;
}

.pricing-box {
    background-color: #fff;
    color: #333;
}

.pricing-box-title {
    background-color: #b4b4b4;
    padding: 1rem;
    text-transform: uppercase;
    font-weight: bold;
}

.pricing-box-info {
    padding: 1rem;
}

.pricing-blue {
    color: aqua;
    font-size: 4rem;
    font-weight: bold;
}

.pricing-box-info p {
    margin: 0.5rem 0;
}

.pricing-more-info {
    margin-top: 2rem;
    display: inline-block;
    background-color: rgb(0, 187, 0);
    padding: 1rem;
    width: 100%;
    color: #fff;
    text-decoration: none;
    border-radius: 5px;
}

.clients {
    padding: 3rem 0;
    text-align: center;
}

.clients-title h3 {
    color: aqua;
    font-size: 2rem;
    font-weight: lighter;
}

.clients-title p {
    color: #b4b4b4;
    margin: 1rem 0;
}

.clients-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    text-align: left;
    margin-top: 2rem;
}

.clients-img {
    display: flex;
    align-items: center;
    margin-top: 2rem;
}

.clients-img img {
    width: 30%;
    border-radius: 100%;
    margin-right: 1rem;
}

.newslatter {
    background: url('https://plus.unsplash.com/premium_photo-1752155109947-539988d49e5d?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxmZWF0dXJlZC1waG90b3MtZmVlZHwyfHx8ZW58MHx8fHx8');
    background-size: cover;
    background-position: center;
    height: 250px;
}

.newslatter-title {
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 250px;
    color: #fff;
}

.newslatter-title h3 {
    font-size: 2rem;
    font-weight: lighter;
}

.newslatter input {
    padding: 0.5rem;
    width: 350px;
    border: none;
    border-radius: 5px;
    margin-right: 1rem;
}

.newslatter button {
    padding: 0.5rem;
    width: 150px;
    background-color: aqua;
    color: #fff;
    border: none;
    border-radius: 5px;
}

footer {
    background-color: aqua;
    padding: 2rem 0;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2rem;
    align-items: center;
}

.footer-menu ul {
    list-style: none;
    display: flex;
    gap: 1rem;
}

.footer-menu a {
    color: #fff;
    text-decoration: none;
    text-transform: uppercase;
    font-weight: 500;
    transition: opacity 0.3s ease;
}

.footer-menu a:hover {
    opacity: 0.8;
}

.footer-logo {
    text-align: center;
}

.footer-logo a {
    color: #fff;
    text-decoration: none;
    font-size: 1.5rem;
    font-weight: bold;
}

.social-media {
    text-align: right;
}

.social-media h4 {
    color: #fff;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 500;
}

.social-links {
    list-style: none;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.social-links li a {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #fff;
    text-decoration: none;
    padding: 0.5rem;
    border-radius: 5px;
    transition: all 0.3s ease;
    background-color: rgba(255, 255, 255, 0.1);
}

.social-links li a:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.social-links li a i {
    font-size: 1.2rem;
}

.social-links li a span {
    font-size: 0.9rem;
    font-weight: 500;
}

/* Social Media Links Styling */
ul {
    list-style: none;
    display: flex;
    justify-content: center;
    gap: 2rem;
    padding: 2rem 0;
    margin: 0;
    background-color: #f8f9fa;
}

ul li a {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    color: #333;
    text-decoration: none;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    transition: all 0.3s ease;
    background-color: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

ul li a:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
}

ul li a i {
    font-size: 2.5rem; /* ทำให้ไอคอนใหญ่ขึ้น */
}

ul li a span {
    font-size: 1.2rem;
    font-weight: 600;
}

/* สีเฉพาะสำหรับแต่ละ social media */
ul li a[href*="facebook"] i {
    color: #1877f2;
}

ul li a[href*="twitter"] i {
    color: #1da1f2;
}

ul li a[href*="tiktok"] i {
    color: #000;
}

ul li a[href*="instagram"] i {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Responsive design for footer */
@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 1.5rem;
    }

    .footer-menu ul {
        justify-content: center;
        flex-wrap: wrap;
    }

    .social-media {
        text-align: center;
    }

    .social-links {
        justify-content: center;
        flex-wrap: wrap;
    }

    /* Responsive สำหรับ social media links */
    ul {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }

    ul li a {
        width: 250px;
        justify-content: center;
    }

    ul li a i {
        font-size: 2rem;
    }
}