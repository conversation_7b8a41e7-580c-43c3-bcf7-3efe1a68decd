* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Kanit', sans-serif;
    line-height: 1.6;
    color: #333;
    perspective: 1000px;
    transform-style: preserve-3d;
    scroll-behavior: smooth;
}

.container {
    max-width: 1140px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
}

.section-title {
    text-align: center;
    margin-bottom: 3rem;
    transform: translateZ(20px);
}

.section-title h2 {
    font-size: 2.5rem;
    font-weight: 600;
    color: #00bcd4;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    background: linear-gradient(45deg, #00bcd4, #0097a7, #00bcd4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: shimmer 3s ease-in-out infinite;
}

.section-title p {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

header {
    background: url('https://images.unsplash.com/photo-1533757879476-8f4a3cb1ae4b?q=80&w=1374&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D');
    background-size: cover;
    background-position: center;
    height: 500px;
    color: #fff;
    position: relative;
    transform-style: preserve-3d;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.1));
    z-index: 1;
}

header .container {
    position: relative;
    z-index: 2;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    transform: translateZ(20px);
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

nav a {
    color: #fff;
    text-decoration: none;
    transition: all 0.3s ease;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    font-weight: 500;
    position: relative;
}

nav a:hover {
    transform: translateY(-2px);
    text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.7);
}

nav a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(45deg, #fff, #f0f0f0);
    transition: width 0.3s ease;
}

nav a:hover::after {
    width: 100%;
}

.logo a {
    font-size: 1.8rem;
    font-weight: 700;
    background: linear-gradient(45deg, #fff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-info {
    margin-top: 4rem;
    max-width: 800px;
    transform: translateZ(30px);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateZ(30px) translateY(0px); }
    50% { transform: translateZ(30px) translateY(-10px); }
}

.header-info h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.5);
    background: linear-gradient(45deg, #fff, #f0f0f0, #fff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: shimmer 3s ease-in-out infinite;
}

.header-info h2 {
    font-size: 1.8rem;
    font-weight: 400;
    margin-bottom: 1.5rem;
    color: #f0f0f0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.header-info p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 2rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

@keyframes shimmer {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.contact-info {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.8rem 1.2rem;
    border-radius: 25px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    transform-style: preserve-3d;
}

.contact-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.contact-item i {
    font-size: 1.2rem;
    color: #00bcd4;
}

.contact-item span {
    font-weight: 500;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.header-btn {
    margin-top: 1rem;
    transform: translateZ(20px);
}

.header-btn input {
    padding: 0.8rem;
    width: 230px;
    border: none;
    border-radius: 10px;
    box-shadow:
        inset 0 2px 4px rgba(0, 0, 0, 0.1),
        0 4px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    transform-style: preserve-3d;
}

.header-btn input:focus {
    outline: none;
    transform: translateY(-2px);
    box-shadow:
        inset 0 2px 4px rgba(0, 0, 0, 0.1),
        0 8px 16px rgba(0, 0, 0, 0.3);
}

.header-btn button {
    padding: 0.8rem;
    width: 150px;
    background: linear-gradient(45deg, #00bcd4, #00acc1);
    color: #fff;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    transform-style: preserve-3d;
    box-shadow:
        0 4px 8px rgba(0, 188, 212, 0.3),
        0 8px 16px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.header-btn button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.header-btn button:hover {
    transform: translateY(-4px) rotateX(10deg);
    box-shadow:
        0 8px 16px rgba(0, 188, 212, 0.4),
        0 16px 32px rgba(0, 0, 0, 0.3);
}

.header-btn button:hover::before {
    left: 100%;
}

/* ===== EDUCATION SECTION ===== */
.education {
    padding: 5rem 0;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    position: relative;
    transform-style: preserve-3d;
}

.education-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 3rem;
    perspective: 1000px;
}

.education-item {
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    padding: 2.5rem;
    border-radius: 20px;
    transition: all 0.5s ease;
    transform-style: preserve-3d;
    box-shadow:
        0 10px 20px rgba(0, 0, 0, 0.1),
        0 20px 40px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
    display: flex;
    gap: 2rem;
    align-items: flex-start;
}

.education-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(45deg, #00bcd4, #0097a7);
    transform: scaleX(0);
    transition: transform 0.5s ease;
}

.education-item:hover::before {
    transform: scaleX(1);
}

.education-item:hover {
    transform: translateY(-10px) rotateX(5deg) rotateY(2deg);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.15),
        0 40px 80px rgba(0, 0, 0, 0.08);
}

.education-icon {
    background: linear-gradient(45deg, #00bcd4, #0097a7);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 4px 8px rgba(0, 188, 212, 0.3);
    flex-shrink: 0;
}

.education-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.education-content h4 {
    font-size: 1.2rem;
    font-weight: 500;
    color: #00bcd4;
    margin-bottom: 1rem;
}

.education-content p {
    color: #666;
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

.grade {
    background: linear-gradient(45deg, #4caf50, #45a049);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 500;
    display: inline-block;
    margin-top: 1rem;
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
}

.grade span {
    font-weight: 700;
}

/* ===== EXPERIENCE SECTION ===== */
.experience {
    background: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%);
    color: #fff;
    padding: 5rem 0;
    position: relative;
    transform-style: preserve-3d;
}

.experience::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.experience .section-title {
    position: relative;
    z-index: 2;
    color: white;
}

.experience .section-title h2 {
    color: white;
    background: linear-gradient(45deg, #fff, #f0f0f0, #fff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.experience .section-title p {
    color: rgba(255, 255, 255, 0.9);
}

.experience-timeline {
    position: relative;
    z-index: 2;
    max-width: 1000px;
    margin: 0 auto;
}

.experience-item {
    background: rgba(255, 255, 255, 0.95);
    color: #333;
    margin-bottom: 2rem;
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.5s ease;
    transform-style: preserve-3d;
    box-shadow:
        0 10px 20px rgba(0, 0, 0, 0.2),
        0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.experience-item:hover {
    transform: translateY(-10px) rotateX(5deg);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 40px 80px rgba(0, 0, 0, 0.15);
}

.experience-date {
    background: linear-gradient(45deg, #4caf50, #45a049);
    color: white;
    padding: 1rem 2rem;
    font-weight: 600;
    text-align: center;
}

.experience-content {
    padding: 2rem;
}

.experience-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #00bcd4;
    margin-bottom: 0.5rem;
}

.experience-content h4 {
    font-size: 1.2rem;
    font-weight: 500;
    color: #333;
    margin-bottom: 1.5rem;
}

.experience-content ul {
    list-style: none;
    padding: 0;
}

.experience-content li {
    position: relative;
    padding-left: 2rem;
    margin-bottom: 0.8rem;
    line-height: 1.6;
    color: #666;
}

.experience-content li::before {
    content: '▶';
    position: absolute;
    left: 0;
    color: #00bcd4;
    font-size: 0.8rem;
}

/* ===== SKILLS SECTION ===== */
.skills {
    padding: 5rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
    transform-style: preserve-3d;
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 3rem;
    perspective: 1000px;
}

.skills-category {
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    padding: 2.5rem;
    border-radius: 20px;
    transition: all 0.5s ease;
    transform-style: preserve-3d;
    box-shadow:
        0 10px 20px rgba(0, 0, 0, 0.1),
        0 20px 40px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.skills-category::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(45deg, #00bcd4, #0097a7);
    transform: scaleX(0);
    transition: transform 0.5s ease;
}

.skills-category:hover::before {
    transform: scaleX(1);
}

.skills-category:hover {
    transform: translateY(-10px) rotateX(5deg) rotateY(2deg);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.15),
        0 40px 80px rgba(0, 0, 0, 0.08);
}

.skills-category h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 2rem;
    text-align: center;
    position: relative;
}

.skills-category h3::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: linear-gradient(45deg, #00bcd4, #0097a7);
    border-radius: 2px;
}

.skill-item {
    margin-bottom: 1.5rem;
}

.skill-name {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.skill-name i {
    font-size: 1.2rem;
    color: #00bcd4;
}

.skill-bar {
    background: #e0e0e0;
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.skill-progress {
    height: 100%;
    background: linear-gradient(45deg, #00bcd4, #0097a7);
    border-radius: 4px;
    transition: width 2s ease;
    position: relative;
}

.skill-progress::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s ease-in-out infinite;
}

/* Language Skills */
.language-skills {
    margin-top: 2rem;
}

.language-item {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: rgba(0, 188, 212, 0.05);
    border-radius: 15px;
    border-left: 4px solid #00bcd4;
}

.language-item h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
}

.language-abilities {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.ability {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ability span {
    font-weight: 500;
    color: #666;
    min-width: 60px;
}

.rating {
    display: flex;
    gap: 0.2rem;
}

.rating i {
    color: #ffd700;
    font-size: 1rem;
}

.rating .far {
    color: #ddd;
}

/* Soft Skills */
.soft-skills {
    margin-top: 2rem;
}

.soft-skills h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
}

.soft-skill-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.8rem;
}

.tag {
    background: linear-gradient(45deg, #00bcd4, #0097a7);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: default;
}

.tag:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 188, 212, 0.3);
}

.pricing-box:hover {
    transform: translateY(-20px) rotateX(10deg) rotateY(5deg);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.2),
        0 40px 80px rgba(0, 0, 0, 0.1);
}

.pricing-box:nth-child(2) {
    transform: translateZ(20px);
    border: 3px solid #00bcd4;
}

.pricing-box:nth-child(2):hover {
    transform: translateY(-25px) rotateX(10deg) rotateY(5deg) translateZ(20px);
}

.pricing-box-title {
    background: linear-gradient(145deg, #e0e0e0, #d0d0d0);
    padding: 1.5rem;
    text-transform: uppercase;
    font-weight: bold;
    position: relative;
    overflow: hidden;
}

.pricing-box-title::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.pricing-box:hover .pricing-box-title::after {
    left: 100%;
}

.pricing-box-info {
    padding: 2rem;
    position: relative;
}

.pricing-blue {
    color: #00bcd4;
    font-size: 4rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 188, 212, 0.3);
    background: linear-gradient(45deg, #00bcd4, #0097a7, #00acc1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: shimmer 3s ease-in-out infinite;
}

.pricing-box-info p {
    margin: 0.8rem 0;
    color: #666;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.pricing-more-info {
    margin-top: 2rem;
    display: inline-block;
    background: linear-gradient(45deg, #4caf50, #45a049);
    padding: 1rem 2rem;
    width: 100%;
    color: #fff;
    text-decoration: none;
    border-radius: 10px;
    transition: all 0.3s ease;
    transform-style: preserve-3d;
    box-shadow:
        0 4px 8px rgba(76, 175, 80, 0.3),
        0 8px 16px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.pricing-more-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.pricing-more-info:hover {
    transform: translateY(-2px);
    box-shadow:
        0 8px 16px rgba(76, 175, 80, 0.4),
        0 16px 32px rgba(0, 0, 0, 0.15);
}

.pricing-more-info:hover::before {
    left: 100%;
}

.clients {
    padding: 3rem 0;
    text-align: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
    transform-style: preserve-3d;
}

.clients-title {
    transform: translateZ(20px);
    margin-bottom: 3rem;
}

.clients-title h3 {
    color: #00bcd4;
    font-size: 2rem;
    font-weight: lighter;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    background: linear-gradient(45deg, #00bcd4, #0097a7, #00bcd4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: shimmer 3s ease-in-out infinite;
}

.clients-title p {
    color: #666;
    margin: 1rem 0;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.clients-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    text-align: left;
    margin-top: 2rem;
    perspective: 1000px;
}

.clients-box {
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    padding: 2rem;
    border-radius: 20px;
    transition: all 0.5s ease;
    transform-style: preserve-3d;
    box-shadow:
        0 10px 20px rgba(0, 0, 0, 0.1),
        0 20px 40px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.clients-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(45deg, #00bcd4, #0097a7);
    transform: scaleX(0);
    transition: transform 0.5s ease;
}

.clients-box:hover::before {
    transform: scaleX(1);
}

.clients-box:hover {
    transform: translateY(-10px) rotateX(5deg) rotateY(2deg);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.15),
        0 40px 80px rgba(0, 0, 0, 0.08);
}

.clients-box p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.05);
    position: relative;
    z-index: 2;
}

.clients-img {
    display: flex;
    align-items: center;
    margin-top: 2rem;
    position: relative;
    z-index: 2;
}

.clients-img img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-right: 1rem;
    transition: all 0.3s ease;
    transform-style: preserve-3d;
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.1),
        0 8px 16px rgba(0, 0, 0, 0.05);
}

.clients-img img:hover {
    transform: rotateY(15deg) translateZ(10px);
    box-shadow:
        0 8px 16px rgba(0, 0, 0, 0.15),
        0 16px 32px rgba(0, 0, 0, 0.08);
}

.clients-img p {
    margin: 0;
    font-weight: bold;
    color: #333;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

/* ===== INTERNSHIP INFO SECTION ===== */
.internship-info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 5rem 0;
    position: relative;
    transform-style: preserve-3d;
    overflow: hidden;
}

.internship-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    z-index: 1;
}

.internship-content {
    position: relative;
    z-index: 2;
    color: white;
}

.internship-title {
    text-align: center;
    margin-bottom: 3rem;
    transform: translateZ(30px);
}

.internship-title h2 {
    font-size: 2.5rem;
    font-weight: 600;
    text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.5);
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #fff, #f0f0f0, #fff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: shimmer 3s ease-in-out infinite;
}

.internship-title p {
    font-size: 1.2rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    opacity: 0.9;
}

.internship-details {
    max-width: 800px;
    margin: 0 auto;
    display: grid;
    gap: 2rem;
}

.date-range,
.work-schedule,
.availability {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    transition: all 0.5s ease;
    transform-style: preserve-3d;
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.date-range:hover,
.work-schedule:hover,
.availability:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-5px) rotateX(5deg);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.date-range i,
.work-schedule i,
.availability i {
    font-size: 2rem;
    color: #4caf50;
    background: rgba(255, 255, 255, 0.2);
    padding: 1rem;
    border-radius: 50%;
    flex-shrink: 0;
}

.date-range h3,
.work-schedule h3,
.availability h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #fff;
}

.date-range p,
.work-schedule p,
.availability p {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
}

footer {
    background: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%);
    padding: 3rem 0;
    position: relative;
    transform-style: preserve-3d;
    overflow: hidden;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2rem;
    align-items: center;
}

.footer-menu ul {
    list-style: none;
    display: flex;
    gap: 1rem;
}

.footer-menu a {
    color: #fff;
    text-decoration: none;
    text-transform: uppercase;
    font-weight: 500;
    transition: opacity 0.3s ease;
}

.footer-menu a:hover {
    opacity: 0.8;
}

.footer-logo {
    text-align: center;
}

.footer-logo a {
    color: #fff;
    text-decoration: none;
    font-size: 1.5rem;
    font-weight: bold;
}

.social-media {
    text-align: right;
}

.social-media h4 {
    color: #fff;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 500;
}

.social-links {
    list-style: none;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.social-links li a {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #fff;
    text-decoration: none;
    padding: 0.5rem;
    border-radius: 5px;
    transition: all 0.3s ease;
    background-color: rgba(255, 255, 255, 0.1);
}

.social-links li a:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.social-links li a i {
    font-size: 1.2rem;
}

.social-links li a span {
    font-size: 0.9rem;
    font-weight: 500;
}



/* Responsive design for footer */
@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 1.5rem;
    }

    .footer-menu ul {
        justify-content: center;
        flex-wrap: wrap;
    }

    .social-media {
        text-align: center;
    }

    .social-links {
        justify-content: center;
        flex-wrap: wrap;
    }

}

/* ===== SOCIAL MEDIA 3D BUTTONS ===== */
@import url("https://fonts.googleapis.com/css2?family=Roboto+Condensed:wght@300;400;700&display=swap");

/* Social Media Section */
.social-media-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 80px 0;
    text-align: center;
}

.social-title {
    color: #ffffff;
    font-size: 2.5rem;
    font-weight: 300;
    margin-bottom: 50px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: 2px;
}

/* Social Media Container */
.social-media-list {
    font-family: 'Roboto Condensed', sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin: 0;
    padding: 0;
    list-style: none;
}

/* List Items */
.social-media-list li {
    list-style: none;
    margin: 0;
    position: relative;
}

/* Social Media Icons */
.social-media-list .fa-brands {
    font-size: 2.2rem;
    color: #262626;
    line-height: 80px;
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    padding-right: 16px;
}

/* 3D Button Base */
.social-media-list li a {
    width: 220px;
    height: 80px;
    background: #ffffff;
    text-decoration: none;
    display: flex;
    align-items: center;
    padding-left: 25px;
    position: relative;
    transform: rotate(-30deg) skew(25deg);
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: -20px 20px 20px rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    overflow: hidden;
}

/* Button Text */
.social-media-list li a span {
    color: #262626;
    position: absolute;
    top: 50%;
    left: 70px;
    transform: translateY(-50%);
    letter-spacing: 3px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    text-transform: uppercase;
}

/* 3D Effect - Left Side */
.social-media-list li a::before {
    content: '';
    position: absolute;
    height: 100%;
    width: 25px;
    background: #e0e0e0;
    top: 10px;
    left: -25px;
    transform: rotate(0deg) skewY(-45deg);
    transition: all 0.5s ease;
    border-radius: 8px 0 0 8px;
}

/* 3D Effect - Bottom Side */
.social-media-list li a::after {
    content: '';
    position: absolute;
    height: 25px;
    width: 100%;
    background: #e0e0e0;
    bottom: -25px;
    left: -10px;
    transform: rotate(0deg) skew(-45deg);
    transition: all 0.5s ease;
    border-radius: 0 0 8px 8px;
}

/* Hover Effects */
.social-media-list li a:hover {
    transform: rotate(-30deg) skew(25deg) translate(20px, -15px);
    box-shadow: -40px 40px 40px rgba(0, 0, 0, 0.4);
}

.social-media-list li:hover .fa-brands,
.social-media-list li:hover span {
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Facebook Colors */
.social-media-list li:hover:nth-child(1) a {
    background: linear-gradient(135deg, #3b5998, #4267B2);
}

.social-media-list li:hover:nth-child(1) a::before {
    background: linear-gradient(135deg, #2d4373, #365492);
}

.social-media-list li:hover:nth-child(1) a::after {
    background: linear-gradient(135deg, #4a69ad, #5578c4);
}

/* Twitter Colors */
.social-media-list li:hover:nth-child(2) a {
    background: linear-gradient(135deg, #1da1f2, #0d8bd9);
}

.social-media-list li:hover:nth-child(2) a::before {
    background: linear-gradient(135deg, #0d8bd9, #0a7bc4);
}

.social-media-list li:hover:nth-child(2) a::after {
    background: linear-gradient(135deg, #42a9f3, #1da1f2);
}

/* TikTok Colors */
.social-media-list li:hover:nth-child(3) a {
    background: linear-gradient(135deg, #000000, #333333);
}

.social-media-list li:hover:nth-child(3) a::before {
    background: linear-gradient(135deg, #000000, #1a1a1a);
}

.social-media-list li:hover:nth-child(3) a::after {
    background: linear-gradient(135deg, #333333, #4d4d4d);
}

/* Instagram Colors */
.social-media-list li:hover:nth-child(4) a {
    background: linear-gradient(135deg, #e4405f, #f77737, #fcaf45);
}

.social-media-list li:hover:nth-child(4) a::before {
    background: linear-gradient(135deg, #c13584, #e4405f);
}

.social-media-list li:hover:nth-child(4) a::after {
    background: linear-gradient(135deg, #f77737, #fcaf45);
}

/* Responsive Design */
@media (max-width: 768px) {
    .social-media-section {
        padding: 60px 0;
    }

    .social-title {
        font-size: 2rem;
        margin-bottom: 40px;
    }

    .social-media-list {
        flex-direction: column;
        gap: 30px;
    }

    .social-media-list li a {
        width: 200px;
        height: 70px;
        transform: rotate(-15deg) skew(15deg);
    }

    .social-media-list li a span {
        left: 60px;
        font-size: 1rem;
        letter-spacing: 2px;
    }

    .social-media-list .fa-brands {
        font-size: 1.8rem;
        padding-right: 12px;
    }

    .social-media-list li a:hover {
        transform: rotate(-15deg) skew(15deg) translate(15px, -10px);
    }
}

/* Footer Improvements */
.footer-content {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2rem;
    align-items: center;
    padding: 2rem 0;
}

.footer-menu ul {
    list-style: none;
    display: flex;
    gap: 1.5rem;
    margin: 0;
    padding: 0;
}

.footer-menu a {
    color: #fff;
    text-decoration: none;
    text-transform: uppercase;
    font-weight: 500;
    transition: opacity 0.3s ease;
}

.footer-menu a:hover {
    opacity: 0.8;
}

.footer-logo {
    text-align: center;
}

.footer-logo a {
    color: #fff;
    text-decoration: none;
    font-size: 1.8rem;
    font-weight: bold;
}

.footer-copyright {
    text-align: right;
}

.footer-copyright p {
    color: #fff;
    margin: 0;
    opacity: 0.8;
    font-size: 0.9rem;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }

    /* Header */
    .nav-menu {
        flex-direction: column;
        gap: 1rem;
    }

    .header-info h1 {
        font-size: 2.5rem;
    }

    .header-info h2 {
        font-size: 1.4rem;
    }

    .contact-info {
        flex-direction: column;
        gap: 1rem;
    }

    /* Section titles */
    .section-title h2 {
        font-size: 2rem;
    }

    /* Education */
    .education-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .education-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    /* Experience */
    .experience-item {
        margin-bottom: 1.5rem;
    }

    .experience-content {
        padding: 1.5rem;
    }

    /* Skills */
    .skills-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .skills-category {
        padding: 2rem;
    }

    .language-abilities {
        gap: 0.5rem;
    }

    .ability {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.3rem;
    }

    .soft-skill-tags {
        justify-content: center;
    }

    /* Internship */
    .internship-title h2 {
        font-size: 2rem;
    }

    .internship-details {
        gap: 1.5rem;
    }

    .date-range,
    .work-schedule,
    .availability {
        padding: 1.5rem;
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    /* Social Media */
    .social-media-list {
        flex-direction: column;
        gap: 1.5rem;
        align-items: center;
    }

    .social-media-list li a {
        width: 250px;
        height: 70px;
        transform: rotate(-15deg) skew(15deg);
        justify-content: center;
    }

    .social-media-list li a span {
        left: 60px;
        font-size: 1rem;
        letter-spacing: 2px;
    }

    .social-media-list .fa-brands {
        font-size: 1.8rem;
        padding-right: 12px;
    }

    .social-media-list li a:hover {
        transform: rotate(-15deg) skew(15deg) translate(15px, -10px);
    }

    /* Footer */
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 1.5rem;
    }

    .footer-menu ul {
        justify-content: center;
        flex-wrap: wrap;
    }

    .footer-copyright {
        text-align: center;
    }
}

@media (max-width: 480px) {
    .header-info h1 {
        font-size: 2rem;
    }

    .header-info h2 {
        font-size: 1.2rem;
    }

    .section-title h2 {
        font-size: 1.8rem;
    }

    .social-media-list li a {
        width: 200px;
        height: 60px;
    }

    .social-media-list li a span {
        font-size: 0.9rem;
        left: 50px;
    }

    .social-media-list .fa-brands {
        font-size: 1.5rem;
    }
}