* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.container {
    max-width: 1140px;
    margin: 0 auto;
}

header {
    background: url('https://images.unsplash.com/photo-1533757879476-8f4a3cb1ae4b?q=80&w=1374&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D');
    background-size: cover;
    background-position: center;
    height: 500px;
    color: #fff;
}

nav {
    display: flex;
    justify-content: space-between;
    padding: 1rem 0;
}

nav a {
    color: #fff;
    text-decoration: none;
}

.logo a {
    font-size: 28px;
}

.header-info {
    margin-top: 5rem;
    width: 500px;
}

.header-info h3 {
    font-size: 3rem;
    font-weight: lighter;
}

.header-btn {
    margin-top: 1rem;
}

.header-btn input {
    padding: 0.5rem;
    width: 230px;
    border: none;
    border-radius: 5px;
}

.header-btn button {
    padding: 0.5rem;
    width: 150px;
    background-color: aqua;
    color: #fff;
    border: none;
    border-radius: 5px;
}

.about {
    text-align: center;
    padding: 3rem 0;
}

.about-con {
    padding: 0 10rem;
}

.about-title {
    color: aqua;
    font-size: 2rem;
}

.about-dest {
    color: #b4b4b4;
}

.about-img {
    margin: 2rem 0;
}

.pricing {
    background-color: aqua;
    color: #fff;
    text-align: center;
    padding: 3rem 0;
}

.pricing-info h3 {
    font-weight: lighter;
    font-size: 2rem;
}

.pricing-info p {
    margin: 1rem 0;
}

.pricing-table {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-top: 3rem;
}

.pricing-box {
    background-color: #fff;
    color: #333;
}

.pricing-box-title {
    background-color: #b4b4b4;
    padding: 1rem;
    text-transform: uppercase;
    font-weight: bold;
}

.pricing-box-info {
    padding: 1rem;
}

.pricing-blue {
    color: aqua;
    font-size: 4rem;
    font-weight: bold;
}

.pricing-box-info p {
    margin: 0.5rem 0;
}

.pricing-more-info {
    margin-top: 2rem;
    display: inline-block;
    background-color: rgb(0, 187, 0);
    padding: 1rem;
    width: 100%;
    color: #fff;
    text-decoration: none;
    border-radius: 5px;
}

.clients {
    padding: 3rem 0;
    text-align: center;
}

.clients-title h3 {
    color: aqua;
    font-size: 2rem;
    font-weight: lighter;
}

.clients-title p {
    color: #b4b4b4;
    margin: 1rem 0;
}

.clients-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    text-align: left;
    margin-top: 2rem;
}

.clients-img {
    display: flex;
    align-items: center;
    margin-top: 2rem;
}

.clients-img img {
    width: 30%;
    border-radius: 100%;
    margin-right: 1rem;
}

.newslatter {
    background: url('https://plus.unsplash.com/premium_photo-1752155109947-539988d49e5d?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxmZWF0dXJlZC1waG90b3MtZmVlZHwyfHx8ZW58MHx8fHx8');
    background-size: cover;
    background-position: center;
    height: 250px;
}

.newslatter-title {
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 250px;
    color: #fff;
}

.newslatter-title h3 {
    font-size: 2rem;
    font-weight: lighter;
}

.newslatter input {
    padding: 0.5rem;
    width: 350px;
    border: none;
    border-radius: 5px;
    margin-right: 1rem;
}

.newslatter button {
    padding: 0.5rem;
    width: 150px;
    background-color: aqua;
    color: #fff;
    border: none;
    border-radius: 5px;
}

footer {
    background-color: aqua;
    padding: 2rem 0;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2rem;
    align-items: center;
}

.footer-menu ul {
    list-style: none;
    display: flex;
    gap: 1rem;
}

.footer-menu a {
    color: #fff;
    text-decoration: none;
    text-transform: uppercase;
    font-weight: 500;
    transition: opacity 0.3s ease;
}

.footer-menu a:hover {
    opacity: 0.8;
}

.footer-logo {
    text-align: center;
}

.footer-logo a {
    color: #fff;
    text-decoration: none;
    font-size: 1.5rem;
    font-weight: bold;
}

.social-media {
    text-align: right;
}

.social-media h4 {
    color: #fff;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 500;
}

.social-links {
    list-style: none;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.social-links li a {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #fff;
    text-decoration: none;
    padding: 0.5rem;
    border-radius: 5px;
    transition: all 0.3s ease;
    background-color: rgba(255, 255, 255, 0.1);
}

.social-links li a:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.social-links li a i {
    font-size: 1.2rem;
}

.social-links li a span {
    font-size: 0.9rem;
    font-weight: 500;
}



/* Responsive design for footer */
@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 1.5rem;
    }

    .footer-menu ul {
        justify-content: center;
        flex-wrap: wrap;
    }

    .social-media {
        text-align: center;
    }

    .social-links {
        justify-content: center;
        flex-wrap: wrap;
    }

}

/* ===== SOCIAL MEDIA 3D BUTTONS ===== */
@import url("https://fonts.googleapis.com/css2?family=Roboto+Condensed:wght@300;400;700&display=swap");

/* Social Media Section */
.social-media-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 80px 0;
    text-align: center;
}

.social-title {
    color: #ffffff;
    font-size: 2.5rem;
    font-weight: 300;
    margin-bottom: 50px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: 2px;
}

/* Social Media Container */
.social-media-list {
    font-family: 'Roboto Condensed', sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin: 0;
    padding: 0;
    list-style: none;
}

/* List Items */
.social-media-list li {
    list-style: none;
    margin: 0;
    position: relative;
}

/* Social Media Icons */
.social-media-list .fa-brands {
    font-size: 2.2rem;
    color: #262626;
    line-height: 80px;
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    padding-right: 16px;
}

/* 3D Button Base */
.social-media-list li a {
    width: 220px;
    height: 80px;
    background: #ffffff;
    text-decoration: none;
    display: flex;
    align-items: center;
    padding-left: 25px;
    position: relative;
    transform: rotate(-30deg) skew(25deg);
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: -20px 20px 20px rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    overflow: hidden;
}

/* Button Text */
.social-media-list li a span {
    color: #262626;
    position: absolute;
    top: 50%;
    left: 70px;
    transform: translateY(-50%);
    letter-spacing: 3px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    text-transform: uppercase;
}

/* 3D Effect - Left Side */
.social-media-list li a::before {
    content: '';
    position: absolute;
    height: 100%;
    width: 25px;
    background: #e0e0e0;
    top: 10px;
    left: -25px;
    transform: rotate(0deg) skewY(-45deg);
    transition: all 0.5s ease;
    border-radius: 8px 0 0 8px;
}

/* 3D Effect - Bottom Side */
.social-media-list li a::after {
    content: '';
    position: absolute;
    height: 25px;
    width: 100%;
    background: #e0e0e0;
    bottom: -25px;
    left: -10px;
    transform: rotate(0deg) skew(-45deg);
    transition: all 0.5s ease;
    border-radius: 0 0 8px 8px;
}

/* Hover Effects */
.social-media-list li a:hover {
    transform: rotate(-30deg) skew(25deg) translate(20px, -15px);
    box-shadow: -40px 40px 40px rgba(0, 0, 0, 0.4);
}

.social-media-list li:hover .fa-brands,
.social-media-list li:hover span {
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Facebook Colors */
.social-media-list li:hover:nth-child(1) a {
    background: linear-gradient(135deg, #3b5998, #4267B2);
}

.social-media-list li:hover:nth-child(1) a::before {
    background: linear-gradient(135deg, #2d4373, #365492);
}

.social-media-list li:hover:nth-child(1) a::after {
    background: linear-gradient(135deg, #4a69ad, #5578c4);
}

/* Twitter Colors */
.social-media-list li:hover:nth-child(2) a {
    background: linear-gradient(135deg, #1da1f2, #0d8bd9);
}

.social-media-list li:hover:nth-child(2) a::before {
    background: linear-gradient(135deg, #0d8bd9, #0a7bc4);
}

.social-media-list li:hover:nth-child(2) a::after {
    background: linear-gradient(135deg, #42a9f3, #1da1f2);
}

/* TikTok Colors */
.social-media-list li:hover:nth-child(3) a {
    background: linear-gradient(135deg, #000000, #333333);
}

.social-media-list li:hover:nth-child(3) a::before {
    background: linear-gradient(135deg, #000000, #1a1a1a);
}

.social-media-list li:hover:nth-child(3) a::after {
    background: linear-gradient(135deg, #333333, #4d4d4d);
}

/* Instagram Colors */
.social-media-list li:hover:nth-child(4) a {
    background: linear-gradient(135deg, #e4405f, #f77737, #fcaf45);
}

.social-media-list li:hover:nth-child(4) a::before {
    background: linear-gradient(135deg, #c13584, #e4405f);
}

.social-media-list li:hover:nth-child(4) a::after {
    background: linear-gradient(135deg, #f77737, #fcaf45);
}

/* Responsive Design */
@media (max-width: 768px) {
    .social-media-section {
        padding: 60px 0;
    }

    .social-title {
        font-size: 2rem;
        margin-bottom: 40px;
    }

    .social-media-list {
        flex-direction: column;
        gap: 30px;
    }

    .social-media-list li a {
        width: 200px;
        height: 70px;
        transform: rotate(-15deg) skew(15deg);
    }

    .social-media-list li a span {
        left: 60px;
        font-size: 1rem;
        letter-spacing: 2px;
    }

    .social-media-list .fa-brands {
        font-size: 1.8rem;
        padding-right: 12px;
    }

    .social-media-list li a:hover {
        transform: rotate(-15deg) skew(15deg) translate(15px, -10px);
    }
}

/* Footer Improvements */
.footer-content {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2rem;
    align-items: center;
    padding: 2rem 0;
}

.footer-menu ul {
    list-style: none;
    display: flex;
    gap: 1.5rem;
    margin: 0;
    padding: 0;
}

.footer-menu a {
    color: #fff;
    text-decoration: none;
    text-transform: uppercase;
    font-weight: 500;
    transition: opacity 0.3s ease;
}

.footer-menu a:hover {
    opacity: 0.8;
}

.footer-logo {
    text-align: center;
}

.footer-logo a {
    color: #fff;
    text-decoration: none;
    font-size: 1.8rem;
    font-weight: bold;
}

.footer-copyright {
    text-align: right;
}

.footer-copyright p {
    color: #fff;
    margin: 0;
    opacity: 0.8;
    font-size: 0.9rem;
}

@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 1.5rem;
    }

    .footer-menu ul {
        justify-content: center;
        flex-wrap: wrap;
    }

    .footer-copyright {
        text-align: center;
    }
}