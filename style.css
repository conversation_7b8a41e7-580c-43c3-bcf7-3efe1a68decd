/* ===== MODERN 3-COLOR THEME ===== */
:root {
    --primary-color: #2563eb;    /* Modern Blue */
    --secondary-color: #1e293b;  /* Dark Slate */
    --accent-color: #f8fafc;     /* Light Gray */
    --text-dark: #0f172a;
    --text-light: #64748b;
    --white: #ffffff;
    --shadow: rgba(37, 99, 235, 0.1);
    --shadow-dark: rgba(15, 23, 42, 0.2);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Kanit', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background: var(--accent-color);
    scroll-behavior: smooth;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    position: relative;
}

/* Responsive container */
@media (min-width: 356px) {
    .container { padding: 0 1rem; }
}

@media (min-width: 640px) {
    .container { padding: 0 2rem; }
}

@media (min-width: 1024px) {
    .container { padding: 0 2rem; }
}

/* ===== MODERN SECTION STYLING ===== */
.section-title {
    text-align: center;
    margin-bottom: 3rem;
    padding: 0 1rem;
}

.section-title h2 {
    font-size: clamp(1.8rem, 4vw, 2.5rem);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
    position: relative;
    display: inline-block;
}

.section-title h2::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

.section-title p {
    font-size: clamp(1rem, 2.5vw, 1.1rem);
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.7;
}

/* ===== MODERN HEADER ===== */
header {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
    min-height: 100vh;
    color: var(--white);
    position: relative;
    display: flex;
    align-items: center;
    overflow: hidden;
}

header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    z-index: 1;
}

header .container {
    position: relative;
    z-index: 2;
    width: 100%;
}

/* ===== MODERN NAVIGATION ===== */
nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(30, 41, 59, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    transition: all 0.3s ease;
}

nav .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo a {
    font-size: clamp(1.2rem, 3vw, 1.5rem);
    font-weight: 700;
    color: var(--white);
    text-decoration: none;
    transition: color 0.3s ease;
}

.logo a:hover {
    color: var(--primary-color);
}

.nav-menu {
    display: flex;
    gap: clamp(1rem, 3vw, 2rem);
    list-style: none;
}

.nav-menu a {
    color: var(--white);
    text-decoration: none;
    font-weight: 500;
    font-size: clamp(0.9rem, 2vw, 1rem);
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-menu a:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
}

/* ===== MODERN HEADER CONTENT ===== */
.header-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    align-items: center;
    text-align: center;
    padding: 6rem 0 2rem;
}

@media (min-width: 768px) {
    .header-content {
        grid-template-columns: 300px 1fr;
        text-align: left;
        gap: 3rem;
    }
}

@media (min-width: 1024px) {
    .header-content {
        grid-template-columns: 350px 1fr;
        gap: 4rem;
    }
}

/* ===== MODERN PROFILE IMAGE ===== */
.profile-section {
    justify-self: center;
}

.profile-image {
    position: relative;
    width: 280px;
    height: 320px;
    border-radius: 20px;
    overflow: hidden;
    box-shadow:
        0 20px 40px var(--shadow-dark),
        0 40px 80px rgba(37, 99, 235, 0.1);
    transition: all 0.4s ease;
    border: 3px solid var(--white);
    background: var(--white);
}

.profile-image:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow:
        0 30px 60px var(--shadow-dark),
        0 60px 120px rgba(37, 99, 235, 0.15);
}

.profile-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: all 0.4s ease;
}

.profile-image:hover img {
    transform: scale(1.05);
}

/* Responsive profile image */
@media (max-width: 480px) {
    .profile-image {
        width: 220px;
        height: 260px;
    }
}

@media (min-width: 1024px) {
    .profile-image {
        width: 320px;
        height: 380px;
    }
}

.profile-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(0, 188, 212, 0.15) 0%,
        rgba(0, 151, 167, 0.25) 50%,
        rgba(0, 188, 212, 0.15) 100%);
    opacity: 0;
    transition: opacity 0.5s ease;
}

.profile-image:hover .profile-overlay {
    opacity: 1;
}

/* เพิ่มเอฟเฟกต์แสงขอบ */
.profile-image::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg,
        #00bcd4, #0097a7, #00acc1, #00bcd4);
    border-radius: 22px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.5s ease;
    background-size: 400% 400%;
    animation: gradientShift 3s ease infinite;
}

.profile-image:hover::before {
    opacity: 0.7;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* ===== MODERN HEADER INFO ===== */
.header-info {
    max-width: 600px;
    margin: 0 auto;
}

@media (min-width: 768px) {
    .header-info {
        margin: 0;
    }
}

.header-info h1 {
    font-size: clamp(2rem, 6vw, 3.5rem);
    font-weight: 800;
    margin-bottom: 0.5rem;
    color: var(--white);
    line-height: 1.2;
}

.nickname {
    font-size: clamp(1.2rem, 3vw, 2rem);
    font-weight: 400;
    color: rgba(255, 255, 255, 0.8);
    font-style: italic;
}

.header-info h2 {
    font-size: clamp(1.1rem, 3vw, 1.5rem);
    font-weight: 400;
    margin-bottom: 1.5rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.4;
}

.header-info p {
    font-size: clamp(0.95rem, 2.5vw, 1.1rem);
    line-height: 1.7;
    margin-bottom: 1.5rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 500px;
}

/* ===== PERSONAL INFO ===== */
.personal-info {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
    margin-bottom: 1.5rem;
}

@media (min-width: 768px) {
    .personal-info {
        justify-content: flex-start;
    }
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.6rem 1rem;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.15);
    font-size: clamp(0.85rem, 2vw, 0.95rem);
}

.info-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
}

.info-item i {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.9);
    flex-shrink: 0;
}

.info-item span {
    font-weight: 500;
    color: var(--white);
}

/* ===== MODERN CONTACT INFO ===== */
.contact-info {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
}

@media (min-width: 768px) {
    .contact-info {
        justify-content: flex-start;
    }
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.15);
    padding: 0.75rem 1rem;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    font-size: clamp(0.85rem, 2vw, 0.95rem);
}

.contact-item:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.contact-item i {
    font-size: 1.1rem;
    color: var(--accent-color);
}

.contact-item span {
    font-weight: 500;
    color: var(--white);
}

.header-btn {
    margin-top: 1rem;
    transform: translateZ(20px);
}

.header-btn input {
    padding: 0.8rem;
    width: 230px;
    border: none;
    border-radius: 10px;
    box-shadow:
        inset 0 2px 4px rgba(0, 0, 0, 0.1),
        0 4px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    transform-style: preserve-3d;
}

.header-btn input:focus {
    outline: none;
    transform: translateY(-2px);
    box-shadow:
        inset 0 2px 4px rgba(0, 0, 0, 0.1),
        0 8px 16px rgba(0, 0, 0, 0.3);
}

.header-btn button {
    padding: 0.8rem;
    width: 150px;
    background: linear-gradient(45deg, #00bcd4, #00acc1);
    color: #fff;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    transform-style: preserve-3d;
    box-shadow:
        0 4px 8px rgba(0, 188, 212, 0.3),
        0 8px 16px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.header-btn button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.header-btn button:hover {
    transform: translateY(-4px) rotateX(10deg);
    box-shadow:
        0 8px 16px rgba(0, 188, 212, 0.4),
        0 16px 32px rgba(0, 0, 0, 0.3);
}

.header-btn button:hover::before {
    left: 100%;
}

/* ===== MODERN SECTIONS ===== */
.education, .skills, .internship-info {
    padding: clamp(3rem, 8vw, 6rem) 0;
    background: var(--white);
}

.experience {
    padding: clamp(3rem, 8vw, 6rem) 0;
    background: linear-gradient(135deg, var(--secondary-color) 0%, #0f172a 100%);
    position: relative;
    overflow: hidden;
}

.experience::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.5;
}

/* ===== MODERN CARDS ===== */
.education-grid, .experience-timeline, .skills-grid {
    display: grid;
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.education-grid {
    grid-template-columns: repeat(auto-fit, minmax(min(100%, 400px), 1fr));
}

.education-item, .experience-item, .skills-category {
    background: var(--white);
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 20px var(--shadow);
    transition: all 0.3s ease;
    border: 1px solid rgba(37, 99, 235, 0.1);
}

.education-item:hover, .experience-item:hover, .skills-category:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px var(--shadow);
    border-color: var(--primary-color);
}

.education-item {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 1.5rem;
    align-items: start;
}

@media (max-width: 480px) {
    .education-item {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 1rem;
    }
}

.education-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.5rem;
    flex-shrink: 0;
}

.education-content h3 {
    font-size: clamp(1.2rem, 3vw, 1.4rem);
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.education-content h4 {
    font-size: clamp(1rem, 2.5vw, 1.1rem);
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.education-content p {
    color: var(--text-light);
    margin-bottom: 0.5rem;
    line-height: 1.6;
    font-size: clamp(0.9rem, 2vw, 1rem);
}

.student-info {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-color) !important;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: clamp(0.85rem, 2vw, 0.95rem) !important;
    border-left: 3px solid var(--primary-color);
}

.grade {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 12px;
    font-weight: 600;
    display: inline-block;
    margin-top: 1rem;
    font-size: 0.9rem;
}

.grade span {
    font-weight: 700;
}

/* ===== MODERN EXPERIENCE SECTION ===== */
.experience-timeline {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.experience-item {
    background: rgba(255, 255, 255, 0.95);
    margin-bottom: 2rem;
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.experience-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.25);
    border-color: rgba(255, 255, 255, 0.3);
}

.experience-date {
    background: linear-gradient(135deg, #1e40af, #1e3a8a);
    color: var(--white);
    padding: 1rem 2rem;
    font-weight: 700;
    font-size: 0.95rem;
    text-align: center;
    letter-spacing: 0.5px;
}

.experience-content {
    padding: 2rem;
}

.experience-content h3 {
    font-size: clamp(1.2rem, 3vw, 1.4rem);
    font-weight: 700;
    color: #1e40af;
    margin-bottom: 0.5rem;
}

.experience-content h4 {
    font-size: clamp(1rem, 2.5vw, 1.1rem);
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: 1.5rem;
}

.experience-content ul {
    list-style: none;
    padding: 0;
}

.experience-content li {
    position: relative;
    padding-left: 1.5rem;
    margin-bottom: 0.5rem;
    line-height: 1.6;
    color: var(--text-light);
    font-size: clamp(0.9rem, 2vw, 1rem);
}

.experience-content li::before {
    content: '▶';
    position: absolute;
    left: 0;
    color: #1e40af;
    font-size: 0.8rem;
    top: 0.2rem;
}

/* ===== MODERN SKILLS SECTION ===== */
.skills-grid {
    grid-template-columns: repeat(auto-fit, minmax(min(100%, 400px), 1fr));
}

.skills-category h3 {
    font-size: clamp(1.2rem, 3vw, 1.4rem);
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    text-align: center;
    position: relative;
}

.skills-category h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 3px;
    background: var(--primary-color);
    border-radius: 2px;
}

.skill-item {
    margin-bottom: 1.5rem;
}

.skill-name {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-dark);
    font-size: clamp(0.9rem, 2vw, 1rem);
}

.skill-name i {
    font-size: 1.2rem;
    color: var(--primary-color);
}

.skill-bar {
    background: var(--accent-color);
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.skill-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 4px;
    transition: width 2s ease;
    position: relative;
}

/* ===== MODERN FOOTER ===== */
footer {
    background: var(--secondary-color);
    color: var(--white);
    padding: 3rem 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    align-items: center;
    text-align: center;
}

.footer-menu ul {
    list-style: none;
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.footer-menu a {
    color: var(--white);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    font-size: clamp(0.9rem, 2vw, 1rem);
}

.footer-menu a:hover {
    color: var(--primary-color);
}

.footer-logo a {
    color: var(--white);
    text-decoration: none;
    font-size: clamp(1.2rem, 3vw, 1.5rem);
    font-weight: 700;
}

.footer-copyright p {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    font-size: clamp(0.8rem, 2vw, 0.9rem);
}

/* ===== MODERN SOCIAL MEDIA ===== */
.social-media-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    padding: clamp(3rem, 8vw, 5rem) 0;
    color: var(--white);
}

.social-title {
    color: var(--white);
    font-size: clamp(1.8rem, 4vw, 2.5rem);
    font-weight: 700;
    margin-bottom: 3rem;
    text-align: center;
}

.social-media-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(100%, 250px), 1fr));
    gap: 1.5rem;
    max-width: 800px;
    margin: 0 auto;
    list-style: none;
    padding: 0;
}

.social-media-list li a {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem 1.5rem;
    border-radius: 12px;
    color: var(--white);
    text-decoration: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    font-weight: 500;
}

.social-media-list li a:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.social-media-list li a i {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.social-media-list li a span {
    font-size: clamp(0.9rem, 2vw, 1rem);
}

/* ===== MODERN INTERNSHIP INFO ===== */
.internship-info {
    background: var(--accent-color);
}

.internship-details {
    display: grid;
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.date-range, .work-schedule, .availability {
    background: var(--white);
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 20px var(--shadow);
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 1.5rem;
    align-items: start;
    transition: all 0.3s ease;
    border: 1px solid rgba(37, 99, 235, 0.1);
}

.date-range:hover, .work-schedule:hover, .availability:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px var(--shadow);
    border-color: var(--primary-color);
}

@media (max-width: 640px) {
    .date-range, .work-schedule, .availability {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 1rem;
    }
}

.date-range i, .work-schedule i, .availability i {
    font-size: 2rem;
    color: var(--primary-color);
    background: rgba(37, 99, 235, 0.1);
    padding: 1rem;
    border-radius: 12px;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.date-range h3, .work-schedule h3, .availability h3 {
    font-size: clamp(1.1rem, 3vw, 1.3rem);
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.date-range p, .work-schedule p, .availability p {
    color: var(--text-light);
    line-height: 1.6;
    font-size: clamp(0.9rem, 2vw, 1rem);
}

/* ===== MODERN LANGUAGE SKILLS ===== */
.language-skills {
    margin-top: 2rem;
}

.language-item {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: rgba(37, 99, 235, 0.05);
    border-radius: 12px;
    border-left: 4px solid var(--primary-color);
}

.language-item h4 {
    font-size: clamp(1rem, 2.5vw, 1.2rem);
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.language-abilities {
    display: grid;
    gap: 0.8rem;
}

.ability {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.ability span {
    font-weight: 500;
    color: var(--text-light);
    min-width: 60px;
    font-size: clamp(0.9rem, 2vw, 1rem);
}

.rating {
    display: flex;
    gap: 0.2rem;
}

.rating i {
    color: #fbbf24;
    font-size: 1rem;
}

.rating .far {
    color: #d1d5db;
}

/* ===== MODERN SOFT SKILLS ===== */
.soft-skills {
    margin-top: 2rem;
}

.soft-skills h4 {
    font-size: clamp(1rem, 2.5vw, 1.2rem);
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.soft-skill-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.8rem;
    justify-content: center;
}

@media (min-width: 768px) {
    .soft-skill-tags {
        justify-content: flex-start;
    }
}

.tag {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: clamp(0.8rem, 2vw, 0.9rem);
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: default;
}

.tag:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--shadow);
}

/* Experience section styling */
.experience .section-title {
    position: relative;
    z-index: 2;
}

.experience .section-title h2 {
    color: var(--white);
    background: linear-gradient(45deg, #fff, #e2e8f0, #fff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.experience .section-title h2::after {
    background: linear-gradient(90deg, var(--white), rgba(255, 255, 255, 0.7));
}

.experience .section-title p {
    color: rgba(255, 255, 255, 0.85);
}



/* ===== SKILLS SECTION ===== */
.skills {
    padding: 5rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
    transform-style: preserve-3d;
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 3rem;
    perspective: 1000px;
}

.skills-category {
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    padding: 2.5rem;
    border-radius: 20px;
    transition: all 0.5s ease;
    transform-style: preserve-3d;
    box-shadow:
        0 10px 20px rgba(0, 0, 0, 0.1),
        0 20px 40px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.skills-category::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(45deg, #00bcd4, #0097a7);
    transform: scaleX(0);
    transition: transform 0.5s ease;
}

.skills-category:hover::before {
    transform: scaleX(1);
}

.skills-category:hover {
    transform: translateY(-10px) rotateX(5deg) rotateY(2deg);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.15),
        0 40px 80px rgba(0, 0, 0, 0.08);
}

.skills-category h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 2rem;
    text-align: center;
    position: relative;
}

.skills-category h3::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: linear-gradient(45deg, #00bcd4, #0097a7);
    border-radius: 2px;
}

.skill-item {
    margin-bottom: 1.5rem;
}

.skill-name {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.skill-name i {
    font-size: 1.2rem;
    color: #00bcd4;
}

.skill-bar {
    background: #e0e0e0;
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.skill-progress {
    height: 100%;
    background: linear-gradient(45deg, #00bcd4, #0097a7);
    border-radius: 4px;
    transition: width 2s ease;
    position: relative;
}

.skill-progress::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s ease-in-out infinite;
}

/* Language Skills */
.language-skills {
    margin-top: 2rem;
}

.language-item {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: rgba(0, 188, 212, 0.05);
    border-radius: 15px;
    border-left: 4px solid #00bcd4;
}

.language-item h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
}

.language-abilities {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.ability {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ability span {
    font-weight: 500;
    color: #666;
    min-width: 60px;
}

.rating {
    display: flex;
    gap: 0.2rem;
}

.rating i {
    color: #ffd700;
    font-size: 1rem;
}

.rating .far {
    color: #ddd;
}

/* Soft Skills */
.soft-skills {
    margin-top: 2rem;
}

.soft-skills h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
}

.soft-skill-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.8rem;
}

.tag {
    background: linear-gradient(45deg, #00bcd4, #0097a7);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: default;
}

.tag:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 188, 212, 0.3);
}

.pricing-box:hover {
    transform: translateY(-20px) rotateX(10deg) rotateY(5deg);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.2),
        0 40px 80px rgba(0, 0, 0, 0.1);
}

.pricing-box:nth-child(2) {
    transform: translateZ(20px);
    border: 3px solid #00bcd4;
}

.pricing-box:nth-child(2):hover {
    transform: translateY(-25px) rotateX(10deg) rotateY(5deg) translateZ(20px);
}

.pricing-box-title {
    background: linear-gradient(145deg, #e0e0e0, #d0d0d0);
    padding: 1.5rem;
    text-transform: uppercase;
    font-weight: bold;
    position: relative;
    overflow: hidden;
}

.pricing-box-title::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.pricing-box:hover .pricing-box-title::after {
    left: 100%;
}

.pricing-box-info {
    padding: 2rem;
    position: relative;
}

.pricing-blue {
    color: #00bcd4;
    font-size: 4rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 188, 212, 0.3);
    background: linear-gradient(45deg, #00bcd4, #0097a7, #00acc1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: shimmer 3s ease-in-out infinite;
}

.pricing-box-info p {
    margin: 0.8rem 0;
    color: #666;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.pricing-more-info {
    margin-top: 2rem;
    display: inline-block;
    background: linear-gradient(45deg, #4caf50, #45a049);
    padding: 1rem 2rem;
    width: 100%;
    color: #fff;
    text-decoration: none;
    border-radius: 10px;
    transition: all 0.3s ease;
    transform-style: preserve-3d;
    box-shadow:
        0 4px 8px rgba(76, 175, 80, 0.3),
        0 8px 16px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.pricing-more-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.pricing-more-info:hover {
    transform: translateY(-2px);
    box-shadow:
        0 8px 16px rgba(76, 175, 80, 0.4),
        0 16px 32px rgba(0, 0, 0, 0.15);
}

.pricing-more-info:hover::before {
    left: 100%;
}

.clients {
    padding: 3rem 0;
    text-align: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
    transform-style: preserve-3d;
}

.clients-title {
    transform: translateZ(20px);
    margin-bottom: 3rem;
}

.clients-title h3 {
    color: #00bcd4;
    font-size: 2rem;
    font-weight: lighter;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    background: linear-gradient(45deg, #00bcd4, #0097a7, #00bcd4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: shimmer 3s ease-in-out infinite;
}

.clients-title p {
    color: #666;
    margin: 1rem 0;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.clients-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    text-align: left;
    margin-top: 2rem;
    perspective: 1000px;
}

.clients-box {
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    padding: 2rem;
    border-radius: 20px;
    transition: all 0.5s ease;
    transform-style: preserve-3d;
    box-shadow:
        0 10px 20px rgba(0, 0, 0, 0.1),
        0 20px 40px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.clients-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(45deg, #00bcd4, #0097a7);
    transform: scaleX(0);
    transition: transform 0.5s ease;
}

.clients-box:hover::before {
    transform: scaleX(1);
}

.clients-box:hover {
    transform: translateY(-10px) rotateX(5deg) rotateY(2deg);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.15),
        0 40px 80px rgba(0, 0, 0, 0.08);
}

.clients-box p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.05);
    position: relative;
    z-index: 2;
}

.clients-img {
    display: flex;
    align-items: center;
    margin-top: 2rem;
    position: relative;
    z-index: 2;
}

.clients-img img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-right: 1rem;
    transition: all 0.3s ease;
    transform-style: preserve-3d;
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.1),
        0 8px 16px rgba(0, 0, 0, 0.05);
}

.clients-img img:hover {
    transform: rotateY(15deg) translateZ(10px);
    box-shadow:
        0 8px 16px rgba(0, 0, 0, 0.15),
        0 16px 32px rgba(0, 0, 0, 0.08);
}

.clients-img p {
    margin: 0;
    font-weight: bold;
    color: #333;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

/* ===== INTERNSHIP INFO SECTION ===== */
.internship-info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 5rem 0;
    position: relative;
    transform-style: preserve-3d;
    overflow: hidden;
}

.internship-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    z-index: 1;
}

.internship-content {
    position: relative;
    z-index: 2;
    color: white;
}

.internship-title {
    text-align: center;
    margin-bottom: 3rem;
    transform: translateZ(30px);
}

.internship-title h2 {
    font-size: 2.5rem;
    font-weight: 600;
    text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.5);
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #fff, #f0f0f0, #fff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: shimmer 3s ease-in-out infinite;
}

.internship-title p {
    font-size: 1.2rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    opacity: 0.9;
}

.internship-details {
    max-width: 800px;
    margin: 0 auto;
    display: grid;
    gap: 2rem;
}

.date-range,
.work-schedule,
.availability {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    transition: all 0.5s ease;
    transform-style: preserve-3d;
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.date-range:hover,
.work-schedule:hover,
.availability:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-5px) rotateX(5deg);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.date-range i,
.work-schedule i,
.availability i {
    font-size: 2rem;
    color: #4caf50;
    background: rgba(255, 255, 255, 0.2);
    padding: 1rem;
    border-radius: 50%;
    flex-shrink: 0;
}

.date-range h3,
.work-schedule h3,
.availability h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #fff;
}

.date-range p,
.work-schedule p,
.availability p {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
}

footer {
    background: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%);
    padding: 3rem 0;
    position: relative;
    transform-style: preserve-3d;
    overflow: hidden;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2rem;
    align-items: center;
}

.footer-menu ul {
    list-style: none;
    display: flex;
    gap: 1rem;
}

.footer-menu a {
    color: #fff;
    text-decoration: none;
    text-transform: uppercase;
    font-weight: 500;
    transition: opacity 0.3s ease;
}

.footer-menu a:hover {
    opacity: 0.8;
}

.footer-logo {
    text-align: center;
}

.footer-logo a {
    color: #fff;
    text-decoration: none;
    font-size: 1.5rem;
    font-weight: bold;
}

.social-media {
    text-align: right;
}

.social-media h4 {
    color: #fff;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 500;
}

.social-links {
    list-style: none;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.social-links li a {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #fff;
    text-decoration: none;
    padding: 0.5rem;
    border-radius: 5px;
    transition: all 0.3s ease;
    background-color: rgba(255, 255, 255, 0.1);
}

.social-links li a:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.social-links li a i {
    font-size: 1.2rem;
}

.social-links li a span {
    font-size: 0.9rem;
    font-weight: 500;
}



/* Responsive design for footer */
@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 1.5rem;
    }

    .footer-menu ul {
        justify-content: center;
        flex-wrap: wrap;
    }

    .social-media {
        text-align: center;
    }

    .social-links {
        justify-content: center;
        flex-wrap: wrap;
    }

}

/* ===== SOCIAL MEDIA 3D BUTTONS ===== */
@import url("https://fonts.googleapis.com/css2?family=Roboto+Condensed:wght@300;400;700&display=swap");

/* Social Media Section */
.social-media-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 80px 0;
    text-align: center;
}

.social-title {
    color: #ffffff;
    font-size: 2.5rem;
    font-weight: 300;
    margin-bottom: 50px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: 2px;
}

/* Social Media Container */
.social-media-list {
    font-family: 'Roboto Condensed', sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin: 0;
    padding: 0;
    list-style: none;
}

/* List Items */
.social-media-list li {
    list-style: none;
    margin: 0;
    position: relative;
}

/* Social Media Icons */
.social-media-list .fa-brands {
    font-size: 2.2rem;
    color: #262626;
    line-height: 80px;
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    padding-right: 16px;
}

/* 3D Button Base */
.social-media-list li a {
    width: 220px;
    height: 80px;
    background: #ffffff;
    text-decoration: none;
    display: flex;
    align-items: center;
    padding-left: 25px;
    position: relative;
    transform: rotate(-30deg) skew(25deg);
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: -20px 20px 20px rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    overflow: hidden;
}

/* Button Text */
.social-media-list li a span {
    color: #262626;
    position: absolute;
    top: 50%;
    left: 70px;
    transform: translateY(-50%);
    letter-spacing: 3px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    text-transform: uppercase;
}

/* 3D Effect - Left Side */
.social-media-list li a::before {
    content: '';
    position: absolute;
    height: 100%;
    width: 25px;
    background: #e0e0e0;
    top: 10px;
    left: -25px;
    transform: rotate(0deg) skewY(-45deg);
    transition: all 0.5s ease;
    border-radius: 8px 0 0 8px;
}

/* 3D Effect - Bottom Side */
.social-media-list li a::after {
    content: '';
    position: absolute;
    height: 25px;
    width: 100%;
    background: #e0e0e0;
    bottom: -25px;
    left: -10px;
    transform: rotate(0deg) skew(-45deg);
    transition: all 0.5s ease;
    border-radius: 0 0 8px 8px;
}

/* Hover Effects */
.social-media-list li a:hover {
    transform: rotate(-30deg) skew(25deg) translate(20px, -15px);
    box-shadow: -40px 40px 40px rgba(0, 0, 0, 0.4);
}

.social-media-list li:hover .fa-brands,
.social-media-list li:hover span {
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Facebook Colors */
.social-media-list li:hover:nth-child(1) a {
    background: linear-gradient(135deg, #3b5998, #4267B2);
}

.social-media-list li:hover:nth-child(1) a::before {
    background: linear-gradient(135deg, #2d4373, #365492);
}

.social-media-list li:hover:nth-child(1) a::after {
    background: linear-gradient(135deg, #4a69ad, #5578c4);
}

/* Twitter Colors */
.social-media-list li:hover:nth-child(2) a {
    background: linear-gradient(135deg, #1da1f2, #0d8bd9);
}

.social-media-list li:hover:nth-child(2) a::before {
    background: linear-gradient(135deg, #0d8bd9, #0a7bc4);
}

.social-media-list li:hover:nth-child(2) a::after {
    background: linear-gradient(135deg, #42a9f3, #1da1f2);
}

/* TikTok Colors */
.social-media-list li:hover:nth-child(3) a {
    background: linear-gradient(135deg, #000000, #333333);
}

.social-media-list li:hover:nth-child(3) a::before {
    background: linear-gradient(135deg, #000000, #1a1a1a);
}

.social-media-list li:hover:nth-child(3) a::after {
    background: linear-gradient(135deg, #333333, #4d4d4d);
}

/* Instagram Colors */
.social-media-list li:hover:nth-child(4) a {
    background: linear-gradient(135deg, #e4405f, #f77737, #fcaf45);
}

.social-media-list li:hover:nth-child(4) a::before {
    background: linear-gradient(135deg, #c13584, #e4405f);
}

.social-media-list li:hover:nth-child(4) a::after {
    background: linear-gradient(135deg, #f77737, #fcaf45);
}

/* Responsive Design */
@media (max-width: 768px) {
    .social-media-section {
        padding: 60px 0;
    }

    .social-title {
        font-size: 2rem;
        margin-bottom: 40px;
    }

    .social-media-list {
        flex-direction: column;
        gap: 30px;
    }

    .social-media-list li a {
        width: 200px;
        height: 70px;
        transform: rotate(-15deg) skew(15deg);
    }

    .social-media-list li a span {
        left: 60px;
        font-size: 1rem;
        letter-spacing: 2px;
    }

    .social-media-list .fa-brands {
        font-size: 1.8rem;
        padding-right: 12px;
    }

    .social-media-list li a:hover {
        transform: rotate(-15deg) skew(15deg) translate(15px, -10px);
    }
}

/* Footer Improvements */
.footer-content {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2rem;
    align-items: center;
    padding: 2rem 0;
}

.footer-menu ul {
    list-style: none;
    display: flex;
    gap: 1.5rem;
    margin: 0;
    padding: 0;
}

.footer-menu a {
    color: #fff;
    text-decoration: none;
    text-transform: uppercase;
    font-weight: 500;
    transition: opacity 0.3s ease;
}

.footer-menu a:hover {
    opacity: 0.8;
}

.footer-logo {
    text-align: center;
}

.footer-logo a {
    color: #fff;
    text-decoration: none;
    font-size: 1.8rem;
    font-weight: bold;
}

.footer-copyright {
    text-align: right;
}

.footer-copyright p {
    color: #fff;
    margin: 0;
    opacity: 0.8;
    font-size: 0.9rem;
}

/* ===== RESPONSIVE DESIGN ===== */
/* Mobile First - 356px and up */
@media (min-width: 356px) {
    .container {
        padding: 0 1rem;
    }

    .nav-menu {
        gap: 0.8rem;
    }

    .nav-menu a {
        padding: 0.4rem 0.8rem;
        font-size: 0.85rem;
    }

    .header-content {
        padding: 5rem 0 2rem;
        gap: 1.5rem;
    }

    .contact-info {
        gap: 0.8rem;
    }

    .contact-item {
        padding: 0.6rem 0.8rem;
        font-size: 0.8rem;
    }

    .section-title {
        margin-bottom: 2rem;
    }

    .education-item, .experience-item, .skills-category {
        padding: 1.5rem;
    }
}

/* Small Mobile - 480px and up */
@media (min-width: 480px) {
    .container {
        padding: 0 1.5rem;
    }

    .nav-menu a {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }

    .contact-item {
        font-size: 0.9rem;
        padding: 0.7rem 1rem;
    }

    .education-item, .experience-item, .skills-category {
        padding: 2rem;
    }
}

/* Tablet - 768px and up */
@media (min-width: 768px) {
    .container {
        padding: 0 2rem;
    }

    .nav-menu {
        gap: 1.5rem;
    }

    .header-content {
        padding: 6rem 0 2rem;
        gap: 3rem;
    }

    .contact-info {
        gap: 1.5rem;
    }

    .education-grid, .skills-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }
}

/* Desktop - 1024px and up */
@media (min-width: 1024px) {
    .container {
        padding: 0 2rem;
    }

    .nav-menu {
        gap: 2rem;
    }

    .header-content {
        gap: 4rem;
    }

    .education-grid, .skills-grid {
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    }
}

/* Large Desktop - 1280px and up */
@media (min-width: 1280px) {
    .container {
        max-width: 1200px;
    }

    .header-content {
        gap: 5rem;
    }
}