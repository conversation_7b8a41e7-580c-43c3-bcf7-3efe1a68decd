<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>กฤษติณห์ สิงห์วี - Resume</title>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    
   <header>
    <div class="container">
        <nav>
            <div class="logo">
                <a href="#home">กฤษติณห์ สิงห์วี</a>
            </div>
            <div class="nav-menu">
                <a href="#about">เกี่ยวกับฉัน</a>
                <a href="#experience">ประสบการณ์</a>
                <a href="#education">การศึกษา</a>
                <a href="#skills">ทักษะ</a>
                <a href="#contact">ติดต่อ</a>
            </div>
        </nav>
        <div class="header-info">
            <h1>กฤษติณห์ สิงห์วี</h1>
            <h2>นักศึกษาวิทยาการคอมพิวเตอร์</h2>
            <p>กำลังศึกษาสาขาวิทยาการคอมพิวเตอร์ มีความสนใจด้านเทคโนโลยีใหม่และงานสนับสนุนด้านไอที พร้อมเรียนรู้สิ่งใหม่ และพัฒนาตนเองในตำแหน่ง IT Support อย่างเต็มความสามารถ</p>
            <div class="contact-info">
                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <span><EMAIL></span>
                </div>
                <div class="contact-item">
                    <i class="fas fa-phone"></i>
                    <span>************</span>
                </div>
            </div>
        </div>
    </div>
   </header>

   <section class="education" id="education">
    <div class="container">
        <div class="section-title">
            <h2>ประวัติการศึกษา</h2>
        </div>
        <div class="education-grid">
            <div class="education-item">
                <div class="education-icon">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <div class="education-content">
                    <h3>ระดับปริญญาตรี</h3>
                    <h4>มหาวิทยาลัยราชภัฏกำแพงเพชร</h4>
                    <p>คณะวิทยาศาสตร์และเทคโนโลยี</p>
                    <p>สาขาวิทยาการคอมพิวเตอร์</p>
                    <div class="grade">เกรดเฉลี่ยปัจจุบัน (GPAX): <span>3.01</span></div>
                </div>
            </div>
            <div class="education-item">
                <div class="education-icon">
                    <i class="fas fa-school"></i>
                </div>
                <div class="education-content">
                    <h3>ระดับมัธยมศึกษา</h3>
                    <h4>โรงเรียนคลองขลุงราษฎร์รังสรรค์</h4>
                    <p>แผนการเรียน: ศิลป์ – ภาษาจีน</p>
                    <div class="grade">เกรดเฉลี่ย (GPAX): <span>2.95</span></div>
                </div>
            </div>
        </div>
    </div>
   </section>

   <section class="experience" id="experience">
    <div class="container">
        <div class="section-title">
           <h2>ประสบการณ์การทำงาน</h2>
           <p>ประสบการณ์การทำงานและฝึกงานที่ผ่านมา</p>
        </div>
        <div class="experience-timeline">
            <div class="experience-item">
                <div class="experience-date">
                    <span>6 พ.ค. – 17 มิ.ย. 2568</span>
                </div>
                <div class="experience-content">
                    <h3>นักศึกษาฝึกงานวิชาชีพ</h3>
                    <h4>สำนักศิลปะและวัฒนธรรม มหาวิทยาลัยราชภัฏกำแพงเพชร</h4>
                    <ul>
                        <li>งานธุรการ: เดินเอกสารบัญชี, รับวัสดุ, เตรียมห้องประชุม</li>
                        <li>งานสนับสนุนกิจกรรม: ขนย้ายอุปกรณ์, จัดนิทรรศการ</li>
                        <li>งานออกแบบ: ปกวารสาร, แบบสอบถาม, Google Forms</li>
                        <li>งานไอที: ช่วยแก้ไขปัญหาคอมพิวเตอร์เบื้องต้น</li>
                        <li>กิจกรรมร่วม: "จิบปาทะ" และงานอื่น ๆ ภายในหน่วยงาน</li>
                    </ul>
                </div>
            </div>

            <div class="experience-item">
                <div class="experience-date">
                    <span>16 มี.ค. – 29 เม.ย. 2568</span>
                </div>
                <div class="experience-content">
                    <h3>พนักงานพาร์ทไทม์</h3>
                    <h4>YOUPOT สาขาสะพานใหม่</h4>
                    <ul>
                        <li>ดูแลลูกค้า รับออร์เดอร์หน้าร้านและแอปเดลิเวอรี่</li>
                        <li>ชงเครื่องดื่มตามสูตรเฉพาะ</li>
                        <li>จัดคิวและดูแลความสะอาด</li>
                    </ul>
                </div>
            </div>

            <div class="experience-item">
                <div class="experience-date">
                    <span>17 ธ.ค. 2567 – 14 มี.ค. 2568</span>
                </div>
                <div class="experience-content">
                    <h3>พนักงานพาร์ทไทม์</h3>
                    <h4>Twenty All Cafe</h4>
                    <ul>
                        <li>ให้บริการลูกค้า ดูแลระบบ POS และเงินสด</li>
                        <li>แนะนำเมนูและโปรโมชั่น</li>
                        <li>ประสานงานกับฝ่ายครัวและเดลิเวอรี่</li>
                        <li>ตรวจสอบและเติมสต๊อกวัตถุดิบ</li>
                    </ul>
                </div>
            </div>

            <div class="experience-item">
                <div class="experience-date">
                    <span>29 ม.ค. – 29 พ.ค. 2565</span>
                </div>
                <div class="experience-content">
                    <h3>พนักงานพาร์ทไทม์ (สินค้าอุปโภคบริโภค)</h3>
                    <h4>โลตัส โกเฟรช ท่ามะเขือ</h4>
                    <ul>
                        <li>จัดเรียงสินค้า ตรวจสอบวันหมดอายุ</li>
                        <li>เติมสินค้าเข้าชั้น ดูแลความสะอาด</li>
                        <li>แนะนำสินค้าและชงเครื่องดื่ม</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
   </section>

   <section class="skills" id="skills">
    <div class="container">
        <div class="section-title">
            <h2>ทักษะที่เกี่ยวข้อง</h2>
            <p>ทักษะด้านเทคนิคและภาษาที่มีความสามารถ</p>
        </div>

        <div class="skills-grid">
            <div class="skills-category">
                <h3>ทักษะด้านเทคนิค</h3>
                <div class="skill-item">
                    <div class="skill-name">
                        <i class="fab fa-js-square"></i>
                        <span>JavaScript</span>
                    </div>
                    <div class="skill-bar">
                        <div class="skill-progress" data-width="75%"></div>
                    </div>
                </div>
                <div class="skill-item">
                    <div class="skill-name">
                        <i class="fab fa-css3-alt"></i>
                        <span>CSS</span>
                    </div>
                    <div class="skill-bar">
                        <div class="skill-progress" data-width="80%"></div>
                    </div>
                </div>
                <div class="skill-item">
                    <div class="skill-name">
                        <i class="fab fa-html5"></i>
                        <span>HTML</span>
                    </div>
                    <div class="skill-bar">
                        <div class="skill-progress" data-width="85%"></div>
                    </div>
                </div>
                <div class="skill-item">
                    <div class="skill-name">
                        <i class="fab fa-php"></i>
                        <span>PHP</span>
                    </div>
                    <div class="skill-bar">
                        <div class="skill-progress" data-width="70%"></div>
                    </div>
                </div>
                <div class="skill-item">
                    <div class="skill-name">
                        <i class="fas fa-file-excel"></i>
                        <span>MS Office</span>
                    </div>
                    <div class="skill-bar">
                        <div class="skill-progress" data-width="90%"></div>
                    </div>
                </div>
            </div>

            <div class="skills-category">
                <h3>ทักษะด้านภาษา</h3>
                <div class="language-skills">
                    <div class="language-item">
                        <h4>ภาษาไทย</h4>
                        <div class="language-abilities">
                            <div class="ability">
                                <span>พูด</span>
                                <div class="rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                            </div>
                            <div class="ability">
                                <span>อ่าน</span>
                                <div class="rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                            </div>
                            <div class="ability">
                                <span>เขียน</span>
                                <div class="rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="far fa-star"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="language-item">
                        <h4>ภาษาอังกฤษ</h4>
                        <div class="language-abilities">
                            <div class="ability">
                                <span>พูด</span>
                                <div class="rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="far fa-star"></i>
                                </div>
                            </div>
                            <div class="ability">
                                <span>อ่าน</span>
                                <div class="rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                            </div>
                            <div class="ability">
                                <span>เขียน</span>
                                <div class="rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="far fa-star"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="soft-skills">
                    <h4>ทักษะอื่น ๆ</h4>
                    <div class="soft-skill-tags">
                        <span class="tag">การสื่อสารในทีม</span>
                        <span class="tag">การแก้ไขปัญหา</span>
                        <span class="tag">การเรียนรู้ด้วยตนเอง</span>
                        <span class="tag">ความรับผิดชอบ</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
   </section>

   <section class="internship-info">
        <div class="container">
            <div class="internship-content">
                <div class="internship-title">
                    <h2>ระยะเวลาที่พร้อมเข้ารับการฝึกงาน</h2>
                    <p>พร้อมเริ่มฝึกงานแบบเต็มเวลาในช่วงเวลาดังต่อไปนี้</p>
                </div>
                <div class="internship-details">
                    <div class="date-range">
                        <i class="fas fa-calendar-alt"></i>
                        <div>
                            <h3>27 ตุลาคม 2568 - 13 กุมภาพันธ์ 2569</h3>
                            <p>ระยะเวลาโดยประมาณ: 3 เดือนครึ่ง (ประมาณ 16 สัปดาห์)</p>
                        </div>
                    </div>
                    <div class="work-schedule">
                        <i class="fas fa-clock"></i>
                        <div>
                            <h3>เวลาทำงาน: จันทร์ – ศุกร์</h3>
                            <p>เวลา 9.00 – 18.00 น. (เต็มเวลา)</p>
                        </div>
                    </div>
                    <div class="availability">
                        <i class="fas fa-check-circle"></i>
                        <div>
                            <h3>ความพร้อม</h3>
                            <p>ช่วงเวลาดังกล่าวไม่มีภาระด้านการเรียนเพิ่มเติม จึงสามารถเข้าร่วมฝึกงานได้อย่างต่อเนื่องตลอดโครงการ</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
   </section>

   <!-- Social Media Section -->
   <section class="social-media-section">
       <div class="container">
           <h2 class="social-title">Connect With Us</h2>
           <ul class="social-media-list">
               <li>
                   <a href="https://facebook.com/kittin.singwee" target="_blank" rel="noopener noreferrer" aria-label="Facebook: kittin singwee">
                       <i class="fa-brands fa-facebook" aria-hidden="true"></i>
                       <span>FB.kittin singwee</span>
                   </a>
               </li>
               <li>
                   <a href="https://instagram.com/kittin_singwee" target="_blank" rel="noopener noreferrer" aria-label="Instagram: kittin_singwee">
                       <i class="fa-brands fa-instagram" aria-hidden="true"></i>
                       <span>IG.kittin_singwee</span>
                   </a>
               </li>
               <li>
                   <a href="mailto:<EMAIL>" aria-label="Email: <EMAIL>">
                       <i class="fas fa-envelope" aria-hidden="true"></i>
                       <span><EMAIL></span>
                   </a>
               </li>
               <li>
                   <a href="tel:************" aria-label="Phone: ************">
                       <i class="fas fa-phone" aria-hidden="true"></i>
                       <span>************</span>
                   </a>
               </li>
           </ul>
       </div>
   </section>

   <footer>
       <div class="container">
           <div class="footer-content">
               <div class="footer-menu">
                   <ul>
                       <li><a href="#education">การศึกษา</a></li>
                       <li><a href="#experience">ประสบการณ์</a></li>
                       <li><a href="#skills">ทักษะ</a></li>
                       <li><a href="#contact">ติดต่อ</a></li>
                   </ul>
               </div>
               <div class="footer-logo">
                   <a href="#home">กฤษติณห์ สิงห์วี</a>
               </div>
               <div class="footer-copyright">
                   <p>&copy; 2024 กฤษติณห์ สิงห์วี - Resume. สงวนลิขสิทธิ์.</p>
               </div>
           </div>
       </div>
   </footer>

    <script>
        // Animate skill bars when they come into view
        const observerOptions = {
            threshold: 0.5,
            rootMargin: '0px 0px -100px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const skillBars = entry.target.querySelectorAll('.skill-progress');
                    skillBars.forEach(bar => {
                        const width = bar.getAttribute('data-width');
                        bar.style.width = width;
                    });
                }
            });
        }, observerOptions);

        // Observe skills section
        document.addEventListener('DOMContentLoaded', () => {
            const skillsSection = document.querySelector('.skills');
            if (skillsSection) {
                observer.observe(skillsSection);
            }

            // Set initial width to 0 for animation
            const skillBars = document.querySelectorAll('.skill-progress');
            skillBars.forEach(bar => {
                bar.style.width = '0%';
            });
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>